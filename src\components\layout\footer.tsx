import Link from 'next/link'
import Image from 'next/image'
import { Facebook, Instagram, Twitter, Mail, Phone, MapPin } from 'lucide-react'
import { STORE_CONFIG } from '@/lib/store-config'

export function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-gradient-to-br from-gray-900 to-gray-800 text-white">
      <div className="container mx-auto px-4 py-8 md:py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
          {/* Company Info */}
          <div className="space-y-4 md:col-span-2 lg:col-span-1">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 rounded-lg overflow-hidden">
                <Image
                  src="/img/logo.jpeg"
                  alt="Acikoo Logo"
                  width={40}
                  height={40}
                  className="w-full h-full object-cover"
                />
              </div>
              <span className="text-xl font-bold text-orange-400">{STORE_CONFIG.name}</span>
            </div>
            <p className="text-gray-300 leading-relaxed text-sm">
              {STORE_CONFIG.description}. Jajanan aci pedas dengan cita rasa autentik yang bikin nagih!
            </p>
            <div className="flex space-x-4">
              <a href={`https://instagram.com/${STORE_CONFIG.contact.instagram.replace('@', '')}`} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-pink-400 transition-colors">
                <Instagram className="w-5 h-5" />
              </a>
              <a href={`https://wa.me/${STORE_CONFIG.contact.whatsapp}`} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-green-400 transition-colors">
                <Phone className="w-5 h-5" />
              </a>
            </div>
          </div>

          {/* Quick Links & Contact */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-orange-400 mb-3">Menu Utama</h3>
              <ul className="space-y-2 text-sm">
                <li>
                  <Link href="/" className="text-gray-300 hover:text-orange-400 transition-colors">
                    🏠 Beranda
                  </Link>
                </li>
                <li>
                  <Link href="/products" className="text-gray-300 hover:text-orange-400 transition-colors">
                    🍽️ Menu Aci Pedas
                  </Link>
                </li>
                <li>
                  <Link href="/store-location" className="text-gray-300 hover:text-orange-400 transition-colors">
                    📍 Lokasi Toko
                  </Link>
                </li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-orange-400 mb-3">Kontak</h3>
              <div className="space-y-2 text-sm">
                <div className="flex items-center space-x-2">
                  <Phone className="w-4 h-4 text-green-400 flex-shrink-0" />
                  <a href={`https://wa.me/${STORE_CONFIG.contact.whatsapp}`} target="_blank" rel="noopener noreferrer" className="text-gray-300 hover:text-green-400 transition-colors">
                    WhatsApp Order
                  </a>
                </div>
                <div className="flex items-start space-x-2">
                  <MapPin className="w-4 h-4 text-orange-400 mt-0.5 flex-shrink-0" />
                  <span className="text-gray-300 text-xs">
                    Pluit, Jakarta Utara
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Quick Info */}
          <div className="space-y-6">
            <div>
              <h3 className="text-lg font-semibold text-orange-400 mb-3">Info Cepat</h3>
              <ul className="space-y-2 text-sm text-gray-300">
                <li>🌶️ Level pedas bisa request</li>
                <li>🚚 Delivery radius 25km</li>
                <li>⏰ Buka 17.00 - 23.00</li>
                <li>💰 QRIS & Cash on Delivery</li>
              </ul>
            </div>

            <div>
              <h3 className="text-lg font-semibold text-orange-400 mb-3">Delivery Zones</h3>
              <div className="grid grid-cols-2 gap-2 text-xs text-gray-300">
                <div>Zone 1-3: Rp 10k-15k</div>
                <div>Zone 4-6: Rp 17k-22k</div>
                <div>Zone 7-9: Rp 25k-30k</div>
                <div className="text-green-400">Max 25km radius</div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-700 mt-8 pt-6">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-3 md:space-y-0">
            <div className="text-gray-400 text-xs md:text-sm text-center md:text-left">
              © {currentYear} {STORE_CONFIG.name}. Made with ❤️ for aci lovers!
            </div>
            <div className="flex space-x-4 text-xs">
              <a href={`https://wa.me/${STORE_CONFIG.contact.whatsapp}`} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-green-400 transition-colors">
                💬 WhatsApp
              </a>
              <a href={STORE_CONFIG.location.googleMapsUrl} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-orange-400 transition-colors">
                📍 Maps
              </a>
              <a href={`https://instagram.com/${STORE_CONFIG.contact.instagram.replace('@', '')}`} target="_blank" rel="noopener noreferrer" className="text-gray-400 hover:text-pink-400 transition-colors">
                📸 IG
              </a>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
