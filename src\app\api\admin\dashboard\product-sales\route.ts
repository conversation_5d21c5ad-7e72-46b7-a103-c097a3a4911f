import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { subDays, startOfDay, endOfDay } from 'date-fns'

// GET /api/admin/dashboard/product-sales - Get product sales analytics
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const startDateParam = searchParams.get('startDate')
    const endDateParam = searchParams.get('endDate')

    // Calculate date range
    let startDate: Date
    let endDate: Date

    if (startDateParam && endDateParam) {
      startDate = startOfDay(new Date(startDateParam))
      endDate = endOfDay(new Date(endDateParam))
    } else {
      // Fallback to default 7 days if no dates provided
      const now = new Date()
      endDate = endOfDay(now)
      startDate = startOfDay(subDays(now, 7))
    }

    // Get product sales data
    const productSales = await prisma.orderItem.groupBy({
      by: ['productId'],
      where: {
        order: {
          createdAt: {
            gte: startDate,
            lte: endDate
          },
          status: { in: ['PAYMENT_VERIFIED', 'SHIPPED', 'DELIVERED'] }
        }
      },
      _sum: {
        quantity: true,
        subtotal: true
      },
      _count: {
        id: true
      }
    })

    // Get product details
    const productIds = productSales.map(item => item.productId)
    const products = await prisma.product.findMany({
      where: { id: { in: productIds } },
      select: { id: true, name: true }
    })

    // Format data for chart
    const formattedData = productSales
      .map(item => {
        const product = products.find(p => p.id === item.productId)
        return {
          name: product?.name || 'Unknown Product',
          sales: item._sum.quantity || 0,
          revenue: Number(item._sum.subtotal || 0),
          orders: item._count.id
        }
      })
      .sort((a, b) => b.sales - a.sales) // Sort by sales count
      .slice(0, 10) // Limit to top 10 products

    return NextResponse.json({
      success: true,
      data: formattedData
    })

  } catch (error) {
    console.error('Get product sales error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
