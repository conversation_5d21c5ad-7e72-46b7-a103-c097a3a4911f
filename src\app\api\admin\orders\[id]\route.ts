import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'

const updateOrderSchema = z.object({
  action: z.enum(['verify_payment', 'process_order', 'ship_order', 'cancel_order'])
})

// PATCH /api/admin/orders/[id] - Update order status (admin actions)
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const session = await getServerSession(authOptions)
    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { id: orderId } = await params
    const body = await request.json()
    const { action } = updateOrderSchema.parse(body)

    const order = await prisma.order.findUnique({
      where: { id: orderId },
      include: { payment: true }
    })

    if (!order) {
      return NextResponse.json({ error: 'Order not found' }, { status: 404 })
    }

    let updateData: any = {}
    let paymentUpdateData: any = {}

    switch (action) {
      case 'verify_payment':
        if (order.status !== 'PENDING_PAYMENT') {
          return NextResponse.json(
            { error: 'Order is not pending payment' },
            { status: 400 }
          )
        }
        updateData.status = 'PAYMENT_VERIFIED'
        paymentUpdateData = {
          status: 'VERIFIED',
          verifiedAt: new Date(),
          verifiedBy: session.user.id
        }
        break

      case 'process_order':
        if (order.status !== 'PAYMENT_VERIFIED') {
          return NextResponse.json(
            { error: 'Payment must be verified first' },
            { status: 400 }
          )
        }
        updateData.status = 'PROCESSING'
        break

      case 'ship_order':
        if (order.status !== 'PROCESSING') {
          return NextResponse.json(
            { error: 'Order must be processed first' },
            { status: 400 }
          )
        }
        updateData.status = 'SHIPPED'
        break

      case 'cancel_order':
        if (['SHIPPED', 'DELIVERED', 'CANCELLED'].includes(order.status)) {
          return NextResponse.json(
            { error: 'Order cannot be cancelled' },
            { status: 400 }
          )
        }
        updateData.status = 'CANCELLED'
        break

      default:
        return NextResponse.json(
          { error: 'Invalid action' },
          { status: 400 }
        )
    }

    // Update order and payment in transaction
    const result = await prisma.$transaction(async (tx) => {
      const updatedOrder = await tx.order.update({
        where: { id: orderId },
        data: updateData,
        include: {
          orderItems: {
            include: {
              product: true
            }
          },
          payment: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
              phone: true
            }
          }
        }
      })

      // Update payment if needed
      if (Object.keys(paymentUpdateData).length > 0) {
        await tx.payment.update({
          where: { orderId: orderId },
          data: paymentUpdateData
        })
      }

      return updatedOrder
    })

    return NextResponse.json({
      success: true,
      data: result
    })

  } catch (error) {
    console.error('Update order error:', error)
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
