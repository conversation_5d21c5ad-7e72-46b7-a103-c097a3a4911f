/**
 * Script untuk menguji sistem zona pengiriman yang baru
 * Menguji: 9 zona dengan jarak maksimum 25km dan kenaikan tarif 2500 per zona
 */

// Import konfigurasi zona dari store-config
const STORE_CONFIG = {
  location: {
    latitude: -6.1275,
    longitude: 106.7906,
    address: 'Jl. Tanah Merah No.15, RT.15/RW.8, Pluit, Kec. Penjaringan, Jakarta Utara, DKI Jakarta 14440'
  },
  delivery: {
    zones: [
      { name: 'Zone 1 (0-3km)', maxDistance: 3, fee: 10000, description: 'Area terdekat dari toko', color: '#22c55e' },
      { name: 'Zone 2 (4-5km)', maxDistance: 5, fee: 12500, description: 'Area Jakarta Utara', color: '#3b82f6' },
      { name: 'Zone 3 (6-7km)', maxDistance: 7, fee: 15000, description: 'Area Jakarta Pusat', color: '#f59e0b' },
      { name: 'Zone 4 (8-9km)', maxDistance: 9, fee: 17500, description: 'Area Jakarta Barat', color: '#f97316' },
      { name: 'Zone 5 (10-12km)', maxDistance: 12, fee: 20000, description: 'Area Jakarta Selatan', color: '#ef4444' },
      { name: 'Zone 6 (13-15km)', maxDistance: 15, fee: 22500, description: 'Area Jabodetabek Dalam', color: '#dc2626' },
      { name: 'Zone 7 (16-18km)', maxDistance: 18, fee: 25000, description: 'Area Jabodetabek', color: '#7c2d12' },
      { name: 'Zone 8 (19-21km)', maxDistance: 21, fee: 27500, description: 'Area Jabodetabek Luar', color: '#991b1b' },
      { name: 'Zone 9 (22-25km)', maxDistance: 25, fee: 30000, description: 'Area Maksimum Pengiriman', color: '#450a0a' }
    ],
    maxDeliveryDistance: 25,
    freeDeliveryThreshold: 50000
  }
}

// Haversine formula untuk menghitung jarak
function calculateDistance(lat1, lng1, lat2, lng2) {
  const R = 6371 // Earth's radius in km
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLng = (lng2 - lng1) * Math.PI / 180
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLng/2) * Math.sin(dLng/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  return R * c
}

// Fungsi untuk mendapatkan info zona
function getDeliveryZoneInfo(lat, lng) {
  const distance = calculateDistance(
    STORE_CONFIG.location.latitude,
    STORE_CONFIG.location.longitude,
    lat,
    lng
  )

  if (distance > STORE_CONFIG.delivery.maxDeliveryDistance) {
    return {
      zone: 0,
      zoneName: 'Di luar area pengiriman',
      deliveryFee: 0,
      distance,
      isDeliverable: false
    }
  }

  const zone = STORE_CONFIG.delivery.zones.find(z => distance <= z.maxDistance)
  if (zone) {
    const zoneIndex = STORE_CONFIG.delivery.zones.indexOf(zone)
    return {
      zone: zoneIndex + 1,
      zoneName: zone.name,
      deliveryFee: zone.fee,
      distance,
      isDeliverable: true,
      zoneData: zone
    }
  }

  return {
    zone: 0,
    zoneName: 'Di luar area pengiriman',
    deliveryFee: 0,
    distance,
    isDeliverable: false
  }
}

// Format currency
function formatCurrency(amount) {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Test locations di berbagai jarak
const testLocations = [
  { name: 'Pluit (Dekat Toko)', lat: -6.1280, lng: 106.7910, expectedZone: 1 },
  { name: 'Kelapa Gading', lat: -6.1500, lng: 106.9000, expectedZone: 2 },
  { name: 'Thamrin (Jakarta Pusat)', lat: -6.1944, lng: 106.8229, expectedZone: 3 },
  { name: 'Grogol (Jakarta Barat)', lat: -6.1500, lng: 106.7900, expectedZone: 4 },
  { name: 'Senayan (Jakarta Selatan)', lat: -6.2297, lng: 106.8075, expectedZone: 5 },
  { name: 'Bekasi Barat', lat: -6.2000, lng: 107.0000, expectedZone: 6 },
  { name: 'Tangerang', lat: -6.1783, lng: 106.6319, expectedZone: 7 },
  { name: 'Depok', lat: -6.4000, lng: 106.8186, expectedZone: 8 },
  { name: 'Bogor', lat: -6.5971, lng: 106.8060, expectedZone: 9 },
  { name: 'Luar Area (>25km)', lat: -6.7000, lng: 106.8000, expectedZone: 0 }
]

function testDeliveryZones() {
  console.log('🧪 Testing New Delivery Zone System')
  console.log('=' .repeat(60))
  console.log(`📍 Store Location: ${STORE_CONFIG.location.address}`)
  console.log(`📏 Maximum Delivery Distance: ${STORE_CONFIG.delivery.maxDeliveryDistance}km`)
  console.log(`🎯 Total Zones: ${STORE_CONFIG.delivery.zones.length}`)
  console.log('')

  // Test zona configuration
  console.log('📋 Zone Configuration:')
  STORE_CONFIG.delivery.zones.forEach((zone, index) => {
    const prevMax = index === 0 ? 0 : STORE_CONFIG.delivery.zones[index - 1].maxDistance
    console.log(`   Zone ${index + 1}: ${prevMax}km - ${zone.maxDistance}km = ${formatCurrency(zone.fee)}`)
  })
  console.log('')

  // Test kenaikan tarif
  console.log('💰 Tariff Progression Test:')
  let prevFee = 0
  STORE_CONFIG.delivery.zones.forEach((zone, index) => {
    const increase = index === 0 ? 0 : zone.fee - prevFee
    console.log(`   ${zone.name}: ${formatCurrency(zone.fee)} ${index > 0 ? `(+${formatCurrency(increase)})` : '(Base)'}`)
    prevFee = zone.fee
  })
  console.log('')

  // Test locations
  console.log('🗺️  Location Testing:')
  console.log('-'.repeat(80))
  console.log('Location'.padEnd(25) + 'Distance'.padEnd(12) + 'Zone'.padEnd(20) + 'Fee'.padEnd(15) + 'Status')
  console.log('-'.repeat(80))

  let passedTests = 0
  let totalTests = testLocations.length

  testLocations.forEach(location => {
    const result = getDeliveryZoneInfo(location.lat, location.lng)
    const status = result.zone === location.expectedZone ? '✅ PASS' : '❌ FAIL'
    
    if (result.zone === location.expectedZone) {
      passedTests++
    }

    console.log(
      location.name.padEnd(25) +
      `${result.distance.toFixed(1)}km`.padEnd(12) +
      result.zoneName.padEnd(20) +
      formatCurrency(result.deliveryFee).padEnd(15) +
      status
    )
  })

  console.log('-'.repeat(80))
  console.log('')

  // Test summary
  console.log('📊 Test Summary:')
  console.log(`   ✅ Passed: ${passedTests}/${totalTests}`)
  console.log(`   ❌ Failed: ${totalTests - passedTests}/${totalTests}`)
  console.log(`   📈 Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`)
  console.log('')

  // Test edge cases
  console.log('🔍 Edge Case Testing:')
  
  // Test exact boundary
  const boundaryTest = getDeliveryZoneInfo(-6.1275, 106.7906 + (25 / 111.32)) // Approximately 25km east
  console.log(`   Boundary (≈25km): ${boundaryTest.distance.toFixed(1)}km - ${boundaryTest.isDeliverable ? 'Deliverable' : 'Not Deliverable'}`)
  
  // Test just over boundary
  const overBoundaryTest = getDeliveryZoneInfo(-6.1275, 106.7906 + (26 / 111.32)) // Approximately 26km east
  console.log(`   Over Boundary (≈26km): ${overBoundaryTest.distance.toFixed(1)}km - ${overBoundaryTest.isDeliverable ? 'Deliverable' : 'Not Deliverable'}`)

  console.log('')
  console.log('🎉 Delivery Zone Testing Complete!')
  
  return passedTests === totalTests
}

// Run the test
const success = testDeliveryZones()
process.exit(success ? 0 : 1)
