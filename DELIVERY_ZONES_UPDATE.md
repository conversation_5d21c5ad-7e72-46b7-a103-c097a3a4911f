# Update Zona Pengiriman & Tarif - Documentation

## 📋 Overview
Perombakan sistem zona pengiriman dengan memperluas jarak maksimum dari 9km menjadi 25km dan mengatur ulang struktur tarif dengan kenaikan 2500 per zona.

## 🎯 Changes Summary

### Before (6 Zones, Max 9km):
```
Zone 1: 0-4km   = Rp 10.000
Zone 2: 5km     = Rp 13.000 (+3.000)
Zone 3: 6km     = Rp 16.000 (+3.000)
Zone 4: 7km     = Rp 19.000 (+3.000)
Zone 5: 8km     = Rp 22.000 (+3.000)
Zone 6: 9km     = Rp 25.000 (+3.000)
```

### After (9 Zones, Max 25km):
```
Zone 1: 0-3km   = Rp 10.000 (Base)
Zone 2: 4-5km   = Rp 12.500 (+2.500)
Zone 3: 6-7km   = Rp 15.000 (+2.500)
Zone 4: 8-9km   = Rp 17.500 (+2.500)
Zone 5: 10-12km = Rp 20.000 (+2.500)
Zone 6: 13-15km = Rp 22.500 (+2.500)
Zone 7: 16-18km = Rp 25.000 (+2.500)
Zone 8: 19-21km = Rp 27.500 (+2.500)
Zone 9: 22-25km = Rp 30.000 (+2.500)
```

## 🔧 Technical Changes

### 1. Store Configuration Update
**File**: `src/lib/store-config.ts`
- Updated `maxDeliveryDistance` from 9km to 25km
- Restructured delivery zones from 6 to 9 zones
- Implemented consistent 2500 tariff increase per zone
- Updated zone descriptions for better coverage

### 2. Map Visualization Updates
**Files**: 
- `src/components/maps/delivery-zone-map.tsx`
- `src/components/maps/store-location-map.tsx`

**Changes**:
- Reduced default zoom from 12 to 10 to accommodate 25km radius
- Map automatically displays all 9 zones with proper colors
- Zone legend updated to show new pricing structure

### 3. UI Component Updates
**File**: `src/app/store-location/page.tsx`
- Updated delivery radius text from 9km to 25km
- Adjusted delivery time estimation from "30-60 minutes" to "45-180 minutes"
- All zone information now displays dynamically from configuration

### 4. Delivery Time Optimization
**File**: `src/lib/store-config.ts`
- Reduced `perKm` from 8 to 5 minutes for better scaling
- Maximum delivery time: 15 + 30 + (25 × 5) = 170 minutes (≈3 hours)
- More reasonable time estimates for longer distances

## 🎨 Zone Color Scheme

| Zone | Distance | Fee | Color | Description |
|------|----------|-----|-------|-------------|
| 1 | 0-3km | Rp 10.000 | Green (#22c55e) | Area terdekat dari toko |
| 2 | 4-5km | Rp 12.500 | Blue (#3b82f6) | Area Jakarta Utara |
| 3 | 6-7km | Rp 15.000 | Yellow (#f59e0b) | Area Jakarta Pusat |
| 4 | 8-9km | Rp 17.500 | Orange (#f97316) | Area Jakarta Barat |
| 5 | 10-12km | Rp 20.000 | Red (#ef4444) | Area Jakarta Selatan |
| 6 | 13-15km | Rp 22.500 | Dark Red (#dc2626) | Area Jabodetabek Dalam |
| 7 | 16-18km | Rp 25.000 | Brown (#7c2d12) | Area Jabodetabek |
| 8 | 19-21km | Rp 27.500 | Dark Red (#991b1b) | Area Jabodetabek Luar |
| 9 | 22-25km | Rp 30.000 | Very Dark Red (#450a0a) | Area Maksimum Pengiriman |

## 📊 Business Impact

### Coverage Expansion:
- **278% increase** in delivery area (from 254km² to 1,963km²)
- Covers entire Jakarta and surrounding areas (Jabodetabek)
- Reaches major cities: Bekasi, Tangerang, Depok, parts of Bogor

### Pricing Strategy:
- **Consistent 2500 increase** per zone for predictable pricing
- **Lower incremental cost** compared to previous 3000 increase
- **Competitive pricing** for extended coverage

### Customer Benefits:
- Much wider delivery coverage
- Clear zone-based pricing structure
- Better delivery time estimates
- Visual zone representation on map

## 🧪 Testing Results

**Test Script**: `scripts/test-delivery-zones.js`

### Zone Configuration ✅
- All 9 zones properly configured
- Consistent 2500 tariff progression
- Maximum distance 25km correctly set

### Boundary Testing ✅
- 24.8km: Deliverable (Zone 9)
- 25.8km: Not Deliverable (Outside area)
- Proper edge case handling

### Integration Testing ✅
- Map visualization shows all zones
- Pricing calculation works correctly
- UI components display updated information

## 🚀 Deployment Checklist

- [x] Update store configuration
- [x] Adjust map zoom levels
- [x] Update UI text and descriptions
- [x] Optimize delivery time calculations
- [x] Test zone boundaries
- [x] Verify pricing calculations
- [x] Update documentation

## 📱 User Experience

### Customer Side:
- Interactive map shows 9 color-coded zones
- Clear pricing for each zone
- Extended delivery coverage
- Realistic time estimates

### Admin Side:
- Zone information automatically calculated
- Order management shows correct zone data
- Delivery analytics support new zones

## 🔍 Monitoring Points

After deployment, monitor:
- Order distribution across new zones
- Customer satisfaction with extended coverage
- Delivery time accuracy for longer distances
- Operational efficiency for 25km radius

---

**Status**: ✅ Implementation Complete
**Coverage**: 25km radius (9 zones)
**Pricing**: Rp 10.000 - Rp 30.000 (2500 increment)
**Testing**: ✅ Passed boundary and integration tests
