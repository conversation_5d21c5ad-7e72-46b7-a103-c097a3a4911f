/**
 * <PERSON><PERSON>t untuk menguji flow status order yang baru
 * Menguji: PENDING_PAYMENT -> PAYMENT_VERIFIED -> PROCESSING -> SHIPPED -> DELIVERED
 */

const { PrismaClient } = require('@prisma/client')
const prisma = new PrismaClient()

async function testOrderFlow() {
  try {
    console.log('🧪 Testing Order Status Flow...\n')

    // 1. <PERSON>i atau buat test user
    let testUser = await prisma.user.findFirst({
      where: { email: '<EMAIL>' }
    })

    if (!testUser) {
      testUser = await prisma.user.create({
        data: {
          email: '<EMAIL>',
          name: 'Test User',
          role: 'CUSTOMER'
        }
      })
      console.log('✅ Created test user')
    }

    // 2. Cari produk untuk test order
    const testProduct = await prisma.product.findFirst({
      where: { isActive: true }
    })

    if (!testProduct) {
      console.log('❌ No active products found. Please create a product first.')
      return
    }

    // 3. Buat test order
    const testOrder = await prisma.order.create({
      data: {
        orderNumber: `TEST-${Date.now()}`,
        userId: testUser.id,
        paymentMethod: 'QRIS',
        subtotal: testProduct.price,
        deliveryFee: 10000,
        total: Number(testProduct.price) + 10000,
        deliveryAddress: 'Test Address, Jakarta',
        deliveryLat: -6.1275,
        deliveryLng: 106.7906,
        status: 'PENDING_PAYMENT',
        orderItems: {
          create: {
            productId: testProduct.id,
            quantity: 1,
            price: testProduct.price,
            subtotal: testProduct.price
          }
        },
        payment: {
          create: {
            method: 'QRIS',
            amount: Number(testProduct.price) + 10000,
            status: 'PENDING'
          }
        }
      },
      include: {
        orderItems: true,
        payment: true
      }
    })

    console.log(`✅ Created test order: ${testOrder.orderNumber}`)
    console.log(`   Status: ${testOrder.status}`)

    // 4. Test status transitions
    const statusFlow = [
      { from: 'PENDING_PAYMENT', to: 'PAYMENT_VERIFIED', description: 'Payment Verification' },
      { from: 'PAYMENT_VERIFIED', to: 'PROCESSING', description: 'Order Processing' },
      { from: 'PROCESSING', to: 'SHIPPED', description: 'Order Shipping' },
      { from: 'SHIPPED', to: 'DELIVERED', description: 'Order Delivery' }
    ]

    let currentOrder = testOrder

    for (const transition of statusFlow) {
      if (currentOrder.status === transition.from) {
        console.log(`\n🔄 Testing: ${transition.description}`)
        console.log(`   ${transition.from} -> ${transition.to}`)

        currentOrder = await prisma.order.update({
          where: { id: currentOrder.id },
          data: { status: transition.to }
        })

        console.log(`✅ Status updated to: ${currentOrder.status}`)
      }
    }

    // 5. Verify final status
    const finalOrder = await prisma.order.findUnique({
      where: { id: testOrder.id },
      include: {
        orderItems: {
          include: { product: true }
        },
        payment: true,
        user: true
      }
    })

    console.log('\n📊 Final Order Status:')
    console.log(`   Order Number: ${finalOrder.orderNumber}`)
    console.log(`   Status: ${finalOrder.status}`)
    console.log(`   Payment Method: ${finalOrder.paymentMethod}`)
    console.log(`   Total: Rp ${finalOrder.total.toLocaleString('id-ID')}`)
    console.log(`   Customer: ${finalOrder.user.name}`)

    // 6. Test status validation
    console.log('\n🔍 Testing Status Validations:')
    
    // Test invalid transition
    try {
      await prisma.order.update({
        where: { id: testOrder.id },
        data: { status: 'PENDING_PAYMENT' } // Invalid: going backwards
      })
      console.log('❌ Should not allow backward status transition')
    } catch (error) {
      console.log('✅ Backward transition properly blocked')
    }

    console.log('\n🎉 Order flow test completed successfully!')
    console.log('\n📋 Status Flow Summary:')
    console.log('   1. PENDING_PAYMENT (Initial)')
    console.log('   2. PAYMENT_VERIFIED (After admin verification)')
    console.log('   3. PROCESSING (After admin processes order)')
    console.log('   4. SHIPPED (After admin ships order)')
    console.log('   5. DELIVERED (Final status)')

  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run the test
testOrderFlow()
