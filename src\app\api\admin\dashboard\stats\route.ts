import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'

// GET /api/admin/dashboard/stats - Get dashboard statistics
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== 'ADMIN') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const startDateParam = searchParams.get('startDate')
    const endDateParam = searchParams.get('endDate')

    // Calculate date range
    let startDate: Date
    let endDate: Date
    let previousStartDate: Date

    if (startDateParam && endDateParam) {
      startDate = new Date(startDateParam)
      endDate = new Date(endDateParam)
      endDate.setHours(23, 59, 59, 999) // Set to end of day

      // Calculate previous period for comparison (same duration)
      const daysDiff = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24))
      previousStartDate = new Date(startDate)
      previousStartDate.setDate(startDate.getDate() - daysDiff)
    } else {
      // Fallback to default 7 days if no dates provided
      const now = new Date()
      endDate = now
      startDate = new Date()
      startDate.setDate(now.getDate() - 7)
      previousStartDate = new Date()
      previousStartDate.setDate(now.getDate() - 14)
    }

    // Get current period stats
    const [
      totalCustomers,
      unreadMessages,
      lowStockProducts,
      previousCustomers,
      totalOrders,
      totalRevenue,
      previousRevenue
    ] = await Promise.all([
      // Current period customers (new registrations)
      prisma.user.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate
          },
          role: 'CUSTOMER'
        }
      }),

      // Unread messages
      prisma.message.count({
        where: {
          isRead: false,
          sender: {
            role: 'CUSTOMER'
          }
        }
      }),

      // Low stock products (stock <= 10)
      prisma.product.count({
        where: {
          stock: { lte: 10 },
          isActive: true
        }
      }),

      // Previous period customers for comparison
      prisma.user.count({
        where: {
          createdAt: {
            gte: previousStartDate,
            lt: startDate
          },
          role: 'CUSTOMER'
        }
      }),

      // Total orders in current period
      prisma.order.count({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        }
      }),

      // Total revenue in current period
      prisma.order.aggregate({
        _sum: { total: true },
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate
          },
          status: { in: ['PAYMENT_VERIFIED', 'SHIPPED', 'DELIVERED'] }
        }
      }),

      // Previous period revenue for comparison
      prisma.order.aggregate({
        _sum: { total: true },
        where: {
          createdAt: {
            gte: previousStartDate,
            lt: startDate
          },
          status: { in: ['PAYMENT_VERIFIED', 'SHIPPED', 'DELIVERED'] }
        }
      })
    ])

    // Calculate growth percentages
    const customerGrowth = previousCustomers > 0
      ? ((totalCustomers - previousCustomers) / previousCustomers) * 100
      : totalCustomers > 0 ? 100 : 0

    const currentRevenue = Number(totalRevenue._sum.total || 0)
    const prevRevenue = Number(previousRevenue._sum.total || 0)
    const revenueGrowth = prevRevenue > 0
      ? ((currentRevenue - prevRevenue) / prevRevenue) * 100
      : currentRevenue > 0 ? 100 : 0

    // Get top selling product
    const topProductData = await prisma.orderItem.groupBy({
      by: ['productId'],
      where: {
        order: {
          createdAt: {
            gte: startDate,
            lte: endDate
          },
          status: { in: ['PAYMENT_VERIFIED', 'SHIPPED', 'DELIVERED'] }
        }
      },
      _sum: {
        quantity: true
      },
      orderBy: {
        _sum: {
          quantity: 'desc'
        }
      },
      take: 1
    })

    let topProduct = null
    if (topProductData.length > 0) {
      const product = await prisma.product.findUnique({
        where: { id: topProductData[0].productId },
        select: { name: true }
      })

      topProduct = {
        name: product?.name || 'Unknown Product',
        salesCount: topProductData[0]._sum.quantity || 0
      }
    }

    // Get additional stats
    const [
      totalCustomersCount,
      totalProductsCount,
      activeProductsCount
    ] = await Promise.all([
      prisma.user.count({
        where: { role: 'CUSTOMER' }
      }),
      prisma.product.count(),
      prisma.product.count({
        where: { isActive: true }
      })
    ])

    return NextResponse.json({
      success: true,
      data: {
        // Main KPIs
        totalRevenue: currentRevenue,
        revenueGrowth: Math.round(revenueGrowth * 100) / 100,
        totalCustomers,
        customerGrowth: Math.round(customerGrowth * 100) / 100,
        totalOrders,
        unreadMessages,
        lowStockProducts,
        topProduct,

        // Additional stats
        totalCustomersCount,
        totalProductsCount,
        activeProductsCount,

        // Period info
        timeRange,
        startDate: startDate.toISOString(),
        endDate: now.toISOString()
      }
    })

  } catch (error) {
    console.error('Get dashboard stats error:', error)
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    )
  }
}
