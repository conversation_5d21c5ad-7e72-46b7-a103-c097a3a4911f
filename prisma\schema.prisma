// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "mysql"
  url      = env("DATABASE_URL")
}

// Enums
enum UserRole {
  CUSTOMER
  ADMIN
}



enum MessageStatus {
  UNREAD
  READ
  REPLIED
}

enum OrderStatus {
  PENDING_PAYMENT
  PAYMENT_VERIFIED
  PROCESSING
  SHIPPED
  DELIVERED
  CANCELLED
}

enum PaymentMethod {
  QRIS
  COD
}

enum PaymentStatus {
  PENDING
  VERIFIED
  FAILED
}

// User model for customers and admins
model User {
  id            String    @id @default(cuid())
  email         String    @unique
  name          String?
  phone         String?
  password      String?
  role          UserRole  @default(CUSTOMER)
  emailVerified DateTime?
  image         String?
  address       String?
  latitude      Float?
  longitude     Float?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts      Account[]      @relation("UserAccounts")
  sessions      Session[]      @relation("UserSessions")
  conversations Conversation[] @relation("CustomerConversations")
  messages      Message[]      @relation("UserMessages")
  orders        Order[]        @relation("UserOrders")

  @@map("users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation("UserAccounts", fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation("UserSessions", fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verification_tokens")
}

// Product Categories
model Category {
  id          String    @id @default(cuid())
  name        String
  description String?
  image       String?
  isActive    Boolean   @default(true)
  createdAt   DateTime  @default(now())
  updatedAt   DateTime  @updatedAt

  // Relations
  products Product[]

  @@map("categories")
}

// Products
model Product {
  id          String        @id @default(cuid())
  name        String
  description String?       @db.Text
  price       Decimal       @db.Decimal(10, 2)
  images      String?       @db.Text // JSON array of image URLs
  stock       Int           @default(0)
  isActive    Boolean       @default(true)
  weight      Float?        // in grams
  categoryId  String
  createdAt   DateTime      @default(now())
  updatedAt   DateTime      @updatedAt

  // Relations
  category   Category    @relation(fields: [categoryId], references: [id])
  orderItems OrderItem[]

  @@map("products")
}

// Simplified Order System
model Order {
  id              String        @id @default(cuid())
  orderNumber     String        @unique
  userId          String
  status          OrderStatus   @default(PENDING_PAYMENT)
  paymentMethod   PaymentMethod
  subtotal        Decimal       @db.Decimal(10, 2)
  deliveryFee     Decimal       @db.Decimal(10, 2)
  total           Decimal       @db.Decimal(10, 2)
  deliveryAddress String        @db.Text
  deliveryLat     Float?
  deliveryLng     Float?
  notes           String?       @db.Text
  createdAt       DateTime      @default(now())
  updatedAt       DateTime      @updatedAt

  // Relations
  user       User        @relation("UserOrders", fields: [userId], references: [id])
  orderItems OrderItem[]
  payment    Payment?

  @@map("orders")
}

model OrderItem {
  id        String  @id @default(cuid())
  orderId   String
  productId String
  quantity  Int
  price     Decimal @db.Decimal(10, 2)
  subtotal  Decimal @db.Decimal(10, 2)

  // Relations
  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id])

  @@map("order_items")
}

model Payment {
  id           String        @id @default(cuid())
  orderId      String        @unique
  method       PaymentMethod
  status       PaymentStatus @default(PENDING)
  amount       Decimal       @db.Decimal(10, 2)
  paymentProof String?       // For QRIS payment proof upload
  verifiedAt   DateTime?
  verifiedBy   String?       // Admin who verified
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt

  // Relations
  order Order @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("payments")
}

// Conversations between customers and admin
model Conversation {
  id         String   @id @default(cuid())
  customerId String
  status     String   @default("ACTIVE") // ACTIVE, CLOSED
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt

  // Relations
  customer User      @relation("CustomerConversations", fields: [customerId], references: [id])
  messages Message[]

  @@map("conversations")
}

// Messages in conversations
model Message {
  id             String   @id @default(cuid())
  conversationId String
  senderId       String
  content        String   @db.Text
  isRead         Boolean  @default(false)
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  // Relations
  conversation Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  sender       User         @relation("UserMessages", fields: [senderId], references: [id])

  @@map("messages")
}



// Content Management
model ContentSection {
  id        String   @id @default(cuid())
  key       String   @unique // e.g., "hero_title", "store_hours"
  title     String
  content   String   @db.Text
  type      String   @default("text") // text, html, json
  isActive  Boolean  @default(true)
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("content_sections")
}
