'use client'

import { useEffect, useRef, useState } from 'react'
import { Search, MapPin, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  STORE_LOCATION,
  DELIVERY_ZONES,
  MAX_DELIVERY_DISTANCE,
  getDeliveryZoneInfo,
  validateDeliveryLocation,
  formatCurrency
} from '@/lib/delivery-pricing'
import { mapManager } from '@/lib/map-manager'

// Store position
const STORE_POSITION = [STORE_LOCATION.lat, STORE_LOCATION.lng] as [number, number]

interface LocationData {
  address: string
  lat: number
  lng: number
  zone: number
  zoneName: string
  deliveryFee: number
  distance: number
}

interface DeliveryZoneMapProps {
  onLocationSelect: (location: LocationData) => void
  onClose: () => void
  initialAddress?: string
}

// Geocoding utilities - consolidated and optimized
async function reverseGeocode(lat: number, lng: number): Promise<string> {
  try {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=18&addressdetails=1`
    )
    const data = await response.json()

    if (data && data.display_name) {
      return data.display_name
    }

    return `${lat.toFixed(6)}, ${lng.toFixed(6)}`
  } catch (error) {
    console.error('Reverse geocoding error:', error)
    return `${lat.toFixed(6)}, ${lng.toFixed(6)}`
  }
}

// Function to search location
async function searchLocation(query: string): Promise<{ lat: number, lng: number, display_name: string } | null> {
  try {
    const response = await fetch(
      `https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query + ', Jakarta')}&limit=1&addressdetails=1`
    )
    const data = await response.json()

    if (data && data.length > 0) {
      return {
        lat: parseFloat(data[0].lat),
        lng: parseFloat(data[0].lon),
        display_name: data[0].display_name
      }
    }

    return null
  } catch (error) {
    console.error('Search location error:', error)
    return null
  }
}

// Function to handle location selection
async function handleLocationSelect(
  lat: number,
  lng: number,
  onLocationSelect: (location: LocationData) => void,
  map?: LeafletMap
) {
  // Get delivery zone info using utility function
  const zoneInfo = getDeliveryZoneInfo(lat, lng)

  // Get address
  const address = await reverseGeocode(lat, lng)

  // Create location data
  const locationData: LocationData = {
    address,
    lat,
    lng,
    zone: zoneInfo.zone,
    zoneName: zoneInfo.zoneName,
    deliveryFee: zoneInfo.deliveryFee,
    distance: zoneInfo.distance
  }

  // Update map view if provided
  if (map) {
    map.setView([lat, lng], 15)
  }

  onLocationSelect(locationData)
}

export function DeliveryZoneMap({ onLocationSelect, onClose, initialAddress }: DeliveryZoneMapProps) {
  const [searchQuery, setSearchQuery] = useState(initialAddress || '')
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(null)
  const [isSearching, setIsSearching] = useState(false)
  const [mounted, setMounted] = useState(false)
  const [mapLoaded, setMapLoaded] = useState(false)
  const mapContainerRef = useRef<HTMLDivElement>(null)
  const mapId = useRef(`delivery-map-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`)
  const mapInstanceRef = useRef<any>(null)

  // Initialize map with enhanced error handling and cleanup
  useEffect(() => {
    let isMounted = true
    setMounted(true)

    const initMap = async () => {
      if (!mapContainerRef.current || !isMounted) return

      try {
        // Ensure any existing map is properly destroyed
        if (mapInstanceRef.current) {
          mapManager.destroyMap(mapId.current)
          mapInstanceRef.current = null
        }

        // Clear container completely
        if (mapContainerRef.current) {
          mapContainerRef.current.innerHTML = ''
          // Remove Leaflet internal ID
          delete (mapContainerRef.current as any)._leaflet_id
        }

        // Wait for DOM to be ready
        await new Promise(resolve => setTimeout(resolve, 100))

        if (!isMounted || !mapContainerRef.current) return

        const map = await mapManager.createMap(
          mapId.current,
          mapContainerRef.current,
          {
            center: STORE_POSITION,
            zoom: 10 // Reduced zoom to accommodate 25km radius
          }
        )

        if (!isMounted) {
          // Component unmounted during initialization
          mapManager.destroyMap(mapId.current)
          return
        }

        mapInstanceRef.current = map

        // Add store marker with custom icon
        const L = await import('leaflet')

        // Create custom store icon
        const storeIcon = L.default.divIcon({
          html: `<div style="background: #dc2626; color: white; border-radius: 50%; width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; font-size: 18px; font-weight: bold; box-shadow: 0 4px 8px rgba(0,0,0,0.3); border: 3px solid white;">🏪</div>`,
          className: 'custom-store-icon',
          iconSize: [40, 40],
          iconAnchor: [20, 20]
        })

        const storeMarker = L.default.marker(STORE_POSITION, { icon: storeIcon }).addTo(map)
        storeMarker.bindPopup(`
          <div style="text-align: center;">
            <strong>🏪 Acikoo Store</strong><br />
            ${STORE_LOCATION.address}
          </div>
        `)

        // Add delivery zones with click handlers
        DELIVERY_ZONES.forEach((zone) => {
          const circle = L.default.circle(STORE_POSITION, {
            color: zone.color,
            fillColor: zone.color,
            fillOpacity: 0.15,
            radius: zone.radius,
            weight: 2,
            interactive: true, // Make zones clickable
            className: `delivery-zone-${zone.id}` // Add CSS class for styling
          }).addTo(map)

          circle.bindPopup(`
            <div style="text-align: center;">
              <strong>${zone.name}</strong><br />
              ${zone.description}<br />
              <span style="color: #dc2626; font-weight: 600;">
                ${formatCurrency(zone.price)}
              </span><br />
              <small style="color: #6b7280; margin-top: 4px; display: block;">
                Klik untuk memilih lokasi di zona ini
              </small>
            </div>
          `)

          // Add click handler for zone selection
          circle.on('click', async (e: any) => {
            if (!isMounted) return

            // Get the center point of the clicked zone as default location
            const { lat, lng } = e.latlng

            // Show popup first
            circle.openPopup()

            // Add a small delay then select location
            setTimeout(async () => {
              await handleMapLocationSelect(lat, lng, map)
            }, 500)
          })

          // Add hover effects
          circle.on('mouseover', () => {
            circle.setStyle({
              fillOpacity: 0.25,
              weight: 3
            })
          })

          circle.on('mouseout', () => {
            circle.setStyle({
              fillOpacity: 0.15,
              weight: 2
            })
          })
        })

        // Add click handler
        map.on('click', async (e: any) => {
          if (!isMounted) return
          const { lat, lng } = e.latlng
          await handleMapLocationSelect(lat, lng, map)
        })

        if (isMounted) {
          setMapLoaded(true)
        }
      } catch (error) {
        console.error('Failed to initialize map:', error)
        if (isMounted) {
          setMapLoaded(false)
        }
      }
    }

    // Delay initialization to ensure DOM is ready
    const timeoutId = setTimeout(initMap, 50)

    return () => {
      isMounted = false
      setMounted(false)
      clearTimeout(timeoutId)

      // Cleanup map instance
      if (mapInstanceRef.current) {
        try {
          mapManager.destroyMap(mapId.current)
        } catch (error) {
          console.warn('Error during map cleanup:', error)
        }
        mapInstanceRef.current = null
      }
    }
  }, [])

  // Handle location selection from map click
  const handleMapLocationSelect = async (lat: number, lng: number, map: any) => {
    try {
      // Get delivery zone info
      const zoneInfo = getDeliveryZoneInfo(lat, lng)

      // Get address
      const address = await reverseGeocode(lat, lng)

      // Create location data
      const locationData: LocationData = {
        address,
        lat,
        lng,
        zone: zoneInfo.zone,
        zoneName: zoneInfo.zoneName,
        deliveryFee: zoneInfo.deliveryFee,
        distance: zoneInfo.distance
      }

      // Add marker for selected location
      const L = await import('leaflet')

      // Remove existing selected marker
      map.eachLayer((layer: any) => {
        if (layer.options && layer.options.isSelectedLocation) {
          map.removeLayer(layer)
        }
      })

      // Create custom selected location icon
      const selectedIcon = L.default.divIcon({
        html: `<div style="background: #2563eb; color: white; border-radius: 50%; width: 32px; height: 32px; display: flex; align-items: center; justify-content: center; font-size: 14px; font-weight: bold; box-shadow: 0 4px 8px rgba(0,0,0,0.3); border: 2px solid white;">📍</div>`,
        className: 'custom-selected-icon',
        iconSize: [32, 32],
        iconAnchor: [16, 16]
      })

      // Add new selected marker
      const marker = L.default.marker([lat, lng], {
        icon: selectedIcon,
        isSelectedLocation: true
      } as any).addTo(map)

      marker.bindPopup(`
        <div style="text-align: center;">
          <strong>📍 Lokasi Terpilih</strong><br />
          ${zoneInfo.zoneName}<br />
          <span style="color: #dc2626; font-weight: 600;">
            ${zoneInfo.deliveryFee > 0
              ? formatCurrency(zoneInfo.deliveryFee)
              : 'Di luar area pengiriman'
            }
          </span>
        </div>
      `).openPopup()

      // Update map view
      map.setView([lat, lng], 15)

      setSelectedLocation(locationData)
    } catch (error) {
      console.error('Error selecting location:', error)
    }
  }

  const handleSearch = async () => {
    if (!searchQuery.trim()) return

    setIsSearching(true)
    try {
      const result = await searchLocation(searchQuery)
      if (result) {
        // Use the stored map instance reference
        const map = mapInstanceRef.current || mapManager.getMap(mapId.current)
        if (map) {
          await handleMapLocationSelect(result.lat, result.lng, map)
        }
      } else {
        alert('Lokasi tidak ditemukan. Coba dengan kata kunci yang lebih spesifik.')
      }
    } catch (error) {
      console.error('Search error:', error)
      alert('Gagal mencari lokasi. Silakan coba lagi.')
    } finally {
      setIsSearching(false)
    }
  }

  const handleConfirmLocation = () => {
    if (selectedLocation) {
      onLocationSelect(selectedLocation)
      onClose()
    }
  }

  // Don't render until mounted
  if (!mounted) return null

  return (
    <div className="fixed inset-0 bg-black/50 z-[60] flex items-start justify-center p-2 sm:p-4 overflow-y-auto">
      <div className="bg-white rounded-lg w-full max-w-4xl min-h-[90vh] max-h-[95vh] flex flex-col my-2 sm:my-4">
        {/* Header */}
        <div className="flex items-center justify-between p-3 sm:p-4 border-b bg-white rounded-t-lg">
          <h3 className="text-lg font-semibold">Pilih Lokasi Pengiriman</h3>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Search */}
        <div className="p-3 sm:p-4 border-b bg-white">
          <div className="flex space-x-2">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Cari alamat di Jakarta..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleSearch()}
                className="pl-10"
              />
            </div>
            <Button
              onClick={handleSearch}
              disabled={isSearching || !searchQuery.trim()}
              className="bg-red-600 hover:bg-red-700 px-3 sm:px-4"
            >
              {isSearching ? 'Mencari...' : 'Cari'}
            </Button>
          </div>
        </div>

        {/* Map Container */}
        <div className="flex-1 relative min-h-[300px] sm:min-h-[400px]">
          {!mapLoaded && (
            <div className="absolute inset-0 bg-gray-100 flex items-center justify-center">
              <div className="text-center">
                <div className="w-8 h-8 border-4 border-red-600 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                <p className="text-gray-600">Memuat peta...</p>
              </div>
            </div>
          )}
          <div
            ref={mapContainerRef}
            className="h-full w-full"
            style={{ minHeight: '300px' }}
          />
        </div>

        {/* Selected Location Info */}
        {selectedLocation && (
          <div className="p-4 border-t bg-gray-50">
            <Card>
              <CardContent className="p-4">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <MapPin className="w-4 h-4 text-red-600" />
                      <span className="font-medium">Lokasi Terpilih</span>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">{selectedLocation.address}</p>
                    <div className="flex items-center space-x-4">
                      <Badge
                        variant={selectedLocation.zone > 0 ? "default" : "destructive"}
                        className={selectedLocation.zone > 0 ? "bg-blue-600" : ""}
                      >
                        {selectedLocation.zoneName}
                      </Badge>
                      <span className="text-sm text-gray-600">
                        Jarak: {selectedLocation.distance.toFixed(1)} km
                      </span>
                      {selectedLocation.deliveryFee > 0 && (
                        <span className="font-semibold text-red-600">
                          {formatCurrency(selectedLocation.deliveryFee)}
                        </span>
                      )}
                    </div>
                  </div>
                  <Button
                    onClick={handleConfirmLocation}
                    disabled={selectedLocation.zone === 0}
                    className="bg-red-600 hover:bg-red-700"
                  >
                    {selectedLocation.zone === 0 ? 'Di Luar Area' : 'Pilih Lokasi'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Zone Legend */}
        <div className="p-4 border-t">
          <h4 className="font-medium mb-2">Zona Pengiriman:</h4>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
            {DELIVERY_ZONES.map((zone) => (
              <div key={zone.id} className="flex items-center space-x-2">
                <div
                  className="w-3 h-3 rounded-full"
                  style={{ backgroundColor: zone.color }}
                />
                <div className="text-xs">
                  <div className="font-medium">{zone.name}</div>
                  <div className="text-red-600">{formatCurrency(zone.price)}</div>
                </div>
              </div>
            ))}
          </div>
          <div className="mt-2 text-xs text-gray-600">
            <p>💡 Maksimal pengiriman: {MAX_DELIVERY_DISTANCE}km dari toko</p>
          </div>
        </div>
      </div>
    </div>
  )
}
